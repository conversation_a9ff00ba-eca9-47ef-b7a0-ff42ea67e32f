using Newtonsoft.Json.Linq;
using System.Configuration;
using YseStore.Common;
using YseStore.Common.Helper;
using YseStore.IService;
using YseStore.IService.Blog;
using YseStore.IService.Set;
using YseStore.IService.Store;
using YseStore.IService.Visual;
using YseStore.Model.Entities.Blog;
using YseStore.Model.RequestModels.Blog;
using YseStore.Service.Visual;

namespace YseStore.Controllers
{
    public class BlogController : BaseController
    {
        private readonly IBlogNewService _blogNewService;
        private readonly IShippingMethodService _shippingMethodService;
        private readonly IBlogNewTagsService _blogNewTagsService;

        private readonly IBlogNewContentService _blogNewContentService;
        private readonly IBlogReviewService _blogReviewService;
        private readonly ICaptchaService _captchaService;
        private readonly IBlogCategoryService _blogCategoryService;
        private readonly IBlogSeoService _blogSeoService;
        private readonly IBlogCategorySeoService _blogCategorySeoService;
        private readonly IVisualPageBuilderService _visualPageBuilderService;
        private readonly IVisualHelpsService _visualHelpsService;
        
        public BlogController(
            IBlogNewService blogNewService,
            IBlogNewTagsService blogNewTagsService,
            IShippingMethodService shippingMethodThirdService,
            IBlogNewContentService blogNewContentService,
            IBlogReviewService blogReviewService,
            ICaptchaService captchaService,
            IBlogCategoryService blogCategoryService,
            IBlogSeoService blogSeoService,
            IBlogCategorySeoService blogCategorySeoService,
            IMenuService menuService,
            IVisualPageBuilderService visualPageBuilderService,
            IVisualHelpsService visualHelpsService) : base(menuService)
        {
            _blogNewService = blogNewService;
            _blogNewContentService = blogNewContentService;
            _shippingMethodService = shippingMethodThirdService;
            _blogReviewService = blogReviewService;
            _captchaService = captchaService;
            _blogCategoryService = blogCategoryService;
            _blogNewTagsService = blogNewTagsService;
            _blogSeoService = blogSeoService;
            _blogCategorySeoService = blogCategorySeoService;
            _visualPageBuilderService = visualPageBuilderService;
            _visualHelpsService = visualHelpsService;
        }

        public async Task<IActionResult> Index(int page = 1, string keyword = "", string cateId = "", string tagId = "")
        {
            // 设置每页显示的博客数量
            int pageSize = 4;

            // 确保页码有效
            if (page < 1) page = 1;

            try
            {
                // 从服务中获取博客列表数据
                var result = await _blogNewService.GetBlogList(
                    keyword: keyword,
                    status: "published", // 只显示已发布的文章
                    cateId: cateId,
                    tagId: tagId,
                    pageIndex: page,
                    pageSize: pageSize,
                    orderByField: "MyOrder",
                    sortDirection: "DESC"); // 按发布时间倒序排列

                // 获取博客分类列表
                var categoriesResult = await _blogCategoryService.GetCategoryList(
                    keyword: "", // 不过滤分类
                    pageIndex: 1,
                    pageSize: 100, // 获取足够多的分类
                    orderByField: "MyOrder"); // 按排序字段升序排列
                var allTags = await _blogNewTagsService.GetAllTags();



                // 获取可视化页面数据
              
                var visualPageData = await _visualPageBuilderService.BuildVisualPageAsync("blog");
                var settings = visualPageData?.PluginsByType
                                ?.GetValueOrDefault("blog_list") // 安全获取字典值
                                ?.FirstOrDefault()               // 安全获取第一个元素
                                ?.Settings;                      // 设置

                JObject settingsDynamic = JObject.Parse(settings);
                var ContainWidth = _visualHelpsService.SetContainWidth((string)settingsDynamic["ContainerWidth"]);
                //// 获取 ButtonText 的值
                //string buttonText = (string)data["ButtonText"];
                ////string json = visualPageData.ToJsonWithParsedFields();
                // 创建视图模型，导航菜单数据将通过HTMX加载
                var model = new
                {
                    Blogs = result.data,
                    CurrentPage = page,
                    TotalPages = (int)Math.Ceiling(result.dataCount / (double)pageSize),
                    Keyword = keyword,
                    CateId = cateId,
                    TagId = tagId,
                    Categories = categoriesResult.data, // 添加分类数据
                    Tags = allTags,
                    settingsDynamic,
                    ContainWidth
                };
                // 直接将模型传递给视图
                return View(model);
            }
            catch (Exception ex)
            {
                // 直接将模型传递给视图
                return View();
            }
        }

        /// <summary>
        /// 博客分类页面
        /// </summary>
        /// <param name="categoryUrl">分类URL</param>
        /// <param name="page">页码</param>
        /// <param name="keyword">搜索关键词</param>
        /// <param name="tagId">标签ID</param>
        /// <returns></returns>
        [Route("/blog/category/{*categoryUrl:minlength(1)}")]
        public async Task<IActionResult> Category(string categoryUrl, int page = 1, string keyword = "", string tagId = "")
        {
            try
            {
                // 检查分类URL参数
                if (string.IsNullOrWhiteSpace(categoryUrl))
                {
                    return RedirectToAction("Index");
                }

                // 通过BlogCategorySeoService根据URL获取分类ID
                short categoryId = await _blogCategorySeoService.GetCategoryIdByPageUrlAsync(categoryUrl);
                if (categoryId <= 0)
                {
                    return RedirectToAction("Index");
                }

                // 设置每页显示的博客数量
                int pageSize = 4;

                // 确保页码有效
                if (page < 1) page = 1;

                // 从服务中获取博客列表数据
                var result = await _blogNewService.GetBlogList(
                    keyword: keyword,
                    status: "published", // 只显示已发布的文章
                    cateId: categoryId.ToString(),
                    tagId: tagId,
                    pageIndex: page,
                    pageSize: pageSize,
                    orderByField: "MyOrder",
                    sortDirection: "DESC"); // 按发布时间倒序排列

                // 获取博客分类列表
                var categoriesResult = await _blogCategoryService.GetCategoryList(
                    keyword: "", // 不过滤分类
                    pageIndex: 1,
                    pageSize: 100, // 获取足够多的分类
                    orderByField: "MyOrder"); // 按排序字段升序排列
                var allTags = await _blogNewTagsService.GetAllTags();

                // 创建视图模型，导航菜单数据将通过HTMX加载
                var model = new
                {
                    Blogs = result.data,
                    CurrentPage = page,
                    TotalPages = (int)Math.Ceiling(result.dataCount / (double)pageSize),
                    Keyword = keyword,
                    CateId = categoryId.ToString(),
                    TagId = tagId,
                    Categories = categoriesResult.data, // 添加分类数据
                    Tags = allTags,
                    CategoryUrl = categoryUrl // 添加当前分类URL
                };

                // 使用Index视图来显示分类页面
                return View("Index", model);
            }
            catch (Exception ex)
            {
                return RedirectToAction("Index");
            }
        }

        // public IActionResult Detail()
        // {
        //     return View();
        // }
        /// <summary>
        /// 获取博客详情页面
        /// </summary>
        /// <param name="blogUrl">博客URL</param>
        /// <returns></returns>
        [Route("/blog/{*blogUrl:minlength(1)}")]
        public async Task<IActionResult> Detail(string? blogUrl)
        {
            try
            {
                // 检查博客URL参数
                if (string.IsNullOrWhiteSpace(blogUrl))
                {
                    return RedirectToAction("Index");
                }

                // 通过BlogSeoService根据URL获取博客ID
                int id = await _blogSeoService.GetBlogIdByPageUrlAsync(blogUrl);
                if (id <= 0)
                {
                    return RedirectToAction("Index");
                }

                // 获取博客文章
                var blog = await _blogNewService.GetBlogById(id);
                if (blog == null)
                {
                    return RedirectToAction("Index");
                }

                // 获取博客内容
                var content = await _blogNewContentService.GetContentByArticleId(id);

                // 获取相关博客文章
                var relatedBlogs = new List<BlogNew>();
                try
                {
                    // 提取标签和分类
                    string tags = blog.Tag ?? "";
                    uint? categoryId = blog.CateId;

                    // 如果有标签或分类，则查询相关文章
                    if (!string.IsNullOrEmpty(tags) || categoryId.HasValue)
                    {
                        // 通过标签或分类查询相关博客
                        var relatedResult = await _blogNewService.GetBlogList(
                            keyword: "",
                            status: "published",
                            cateId: categoryId.HasValue ? categoryId.Value.ToString() : "",
                            tagId: tags,
                            pageIndex: 1,
                            pageSize: 3);

                        // 过滤掉当前文章自身
                        relatedBlogs = relatedResult.data
                            .Where(b => b.AId != id)
                            .Take(3)
                            .ToList();
                    }

                    // 如果没有找到足够的相关文章，使用最新文章填充
                    if (relatedBlogs.Count < 3)
                    {
                        // 查询最新文章
                        var latestResult = await _blogNewService.GetBlogList(
                            status: "published",
                            pageIndex: 1,
                            pageSize: 10);

                        // 过滤掉当前文章和已有的相关文章
                        var existingIds = relatedBlogs.Select(b => b.AId).ToList();
                        existingIds.Add(id); // 添加当前文章ID

                        var additionalBlogs = latestResult.data
                            .Where(b => !existingIds.Contains(b.AId))
                            .Take(3 - relatedBlogs.Count)
                            .ToList();

                        relatedBlogs.AddRange(additionalBlogs);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"获取相关文章时出错: {ex.Message}");
                }

                // 获取导航菜单数据
                var menuData = await GetNavigationMenusAsync();



                var visualPageData = await _visualPageBuilderService.BuildVisualPageAsync("blog-detail");
                var settings = visualPageData?.PluginsByType
                                ?.GetValueOrDefault("blog_detail") // 安全获取字典值
                                ?.FirstOrDefault()               // 安全获取第一个元素
                                ?.Settings;                      // 设置

                JObject settingsDynamic = JObject.Parse(settings);




                // 创建视图模型，合并导航菜单数据
                var model = new
                {
                    Blog = blog,
                    Content = content,
                    BlogId = id,
                    RelatedBlogs = relatedBlogs,
                    // 添加导航菜单数据
                    NavMenus = ((dynamic)menuData).NavMenus,
                    FooterNavMenus = ((dynamic)menuData).FooterNavMenus,
                    settingsDynamic
                };

                // 尝试增加文章的浏览数
                try
                {
                    _ = _blogNewService.IncrementViewCount(id);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"增加浏览数时出错: {ex.Message}");
                }

                // 直接将模型传递给视图
                return View(model);
            }
            catch (Exception ex)
            {
                // 记录异常以便调试
                Console.WriteLine($"获取博客详情时出错: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");

                return RedirectToAction("Index");
            }
        }


        /// <summary>
        /// 验证博客评论验证码
        /// </summary>
        /// <param name="request">验证码验证请求</param>
        /// <returns>验证结果</returns>
        [HttpPost("api/blog/comment/verify-captcha")]
        public async Task<IActionResult> VerifyCaptcha([FromBody] CaptchaVerifyRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.CaptchaVerifyParam))
                {
                    return Json(new { success = false, message = "验证码参数不能为空" });
                }

                // 使用验证码服务验证
                var verifyResult = await _captchaService.VerifyCaptchaAsync(request.CaptchaVerifyParam);

                return Json(new
                {
                    success = verifyResult.Success,
                    message = verifyResult.Message,
                    code = verifyResult.Code
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"验证验证码时出错: {ex.Message}");
                return Json(new { success = false, message = "验证码验证失败，请重试" });
            }
        }

        /// <summary>
        /// 提交博客评论
        /// </summary>
        /// <param name="request">评论请求对象</param>
        /// <returns>JSON结果</returns>
        [HttpPost("api/blog/comment/submit")]
        public async Task<IActionResult> SubmitComment([FromBody] CommentRequest request)
        {
            try
            {
                // 参数验证
                if (request.BlogId <= 0)
                {
                    return Json(new { success = false, message = "博客ID无效" });
                }

                if (string.IsNullOrWhiteSpace(request.Name))
                {
                    return Json(new { success = false, message = "请输入您的姓名" });
                }

                if (string.IsNullOrWhiteSpace(request.Content))
                {
                    return Json(new { success = false, message = "请输入评论内容" });
                }

                // 验证验证码
                if (string.IsNullOrEmpty(request.CaptchaVerifyParam))
                {
                    return Json(new { success = false, message = "请完成验证码验证" });
                }

                // 创建评论对象
                var review = new BlogReview
                {
                    AId = request.BlogId,
                    Name = request.Name,
                    Email = request.Email,
                    Content = request.Content,
                    AccTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    Praise = 0
                };

                // 保存评论
                long reviewId = await _blogReviewService.AddReview(review);

                if (reviewId > 0)
                {
                    return Json(new { success = true, message = "评论提交成功，感谢您的参与！", data = reviewId });
                }
                else
                {
                    return Json(new { success = false, message = "评论提交失败，请稍后再试" });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"提交评论时出错: {ex.Message}");
                return Json(new { success = false, message = "系统错误，请稍后再试" });
            }
        }

        /// <summary>
        /// 提交回复
        /// </summary>
        /// <param name="reviewId">评论ID</param>
        /// <param name="request">回复请求对象</param>
        /// <returns>JSON结果</returns>
        [HttpPost("api/blog/comment/{reviewId}/reply")]
        public async Task<IActionResult> SubmitReply(int reviewId, [FromBody] ReplyRequest request)
        {
            try
            {
                // 参数验证
                if (reviewId <= 0)
                {
                    return Json(new { success = false, message = "评论ID无效" });
                }

                if (string.IsNullOrWhiteSpace(request.Name))
                {
                    return Json(new { success = false, message = "请输入您的姓名" });
                }

                if (string.IsNullOrWhiteSpace(request.Content))
                {
                    return Json(new { success = false, message = "请输入回复内容" });
                }

                // 验证验证码
                if (string.IsNullOrEmpty(request.CaptchaVerifyParam))
                {
                    return Json(new { success = false, message = "请完成验证码验证" });
                }

                // 验证评论是否存在
                var review = await _blogReviewService.GetReviewById(reviewId);
                if (review == null)
                {
                    return Json(new { success = false, message = "评论不存在" });
                }

                // 如果指定了回复目标ID，验证回复目标是否存在
                if (request.ReplyToId.HasValue && request.ReplyToId.Value > 0)
                {
                    var targetReply = await _blogReviewService.GetReplyById(request.ReplyToId.Value);
                    if (targetReply == null)
                    {
                        return Json(new { success = false, message = "回复目标不存在" });
                    }

                    // 确保回复目标属于当前评论
                    if (targetReply.ReviewId != reviewId)
                    {
                        return Json(new { success = false, message = "回复目标不属于当前评论" });
                    }
                }

                // 创建回复对象
                var reply = new BlogReply
                {
                    ReviewId = reviewId,
                    Content = request.Content,
                    ReplyName = request.Name,
                    ReplyTime = DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now),
                    IsAdmin = request.IsAdmin,
                    ReplyToId = request.ReplyToId.HasValue ? request.ReplyToId.Value : 0,
                    ReplyToName = request.ReplyToName
                };

                // 保存回复
                long replyId = await _blogReviewService.AddReply(reply);

                if (replyId > 0)
                {
                    return Json(new { success = true, message = "回复提交成功，感谢您的参与！", data = replyId });
                }
                else
                {
                    return Json(new { success = false, message = "回复提交失败，请稍后再试" });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"提交回复时出错: {ex.Message}");
                return Json(new { success = false, message = "系统错误，请稍后再试" });
            }
        }
    }
}